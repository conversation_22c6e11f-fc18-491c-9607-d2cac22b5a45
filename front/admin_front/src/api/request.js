import axios from "axios";
import { getToken, handleLoginExpired } from "@/utils/token";

// 创建axios实例
const service = axios.create({
  // baseURL: process.env.VUE_APP_API_BASE_URL || "/api", // 默认API地址

  baseURL: "/api",
  timeout: 15000,
  withCredentials: true, // 支持跨域cookie
});

// 创建文件上传专用的axios实例
const uploadService = axios.create({
  // baseURL: "http://118.24.74.226:8082/api",
  baseURL: "/api",
  timeout: 0, // 超时时间无限，适合大文件上传
  withCredentials: true, // 支持跨域cookie
});

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 使用工具函数获取token
    const token = getToken();
    if (token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    console.error("请求错误：", error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    const res = response.data;

    // 如果code不为200，表示请求异常
    if (res.code !== 200) {
      window.$message.error(res.msg || "请求失败");
      return Promise.reject(new Error(res.msg || "请求失败"));
    } else {
      return res;
    }
  },
  (error) => {
    console.error("响应错误：", error);

    // 处理HTTP状态码401
    if (error.response && error.response.status === 401) {
      // 使用统一的登录失效处理
      handleLoginExpired(error.response.data?.msg || "登录已过期，请重新登录");
      return Promise.reject(error);
    }

    // 处理业务错误码401（如果response.data存在且code为401）
    if (
      error.response &&
      error.response.data &&
      error.response.data.code === 401
    ) {
      handleLoginExpired(error.response.data.msg || "登录已过期，请重新登录");
      return Promise.reject(error);
    }

    window.$message.error(error.response.data.msg || "网络请求异常");
    return Promise.reject(error);
  }
);

// 文件上传请求拦截器
uploadService.interceptors.request.use(
  (config) => {
    // 使用工具函数获取token
    const token = getToken();
    if (token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    console.error("上传请求错误：", error);
    return Promise.reject(error);
  }
);

// 文件上传响应拦截器
uploadService.interceptors.response.use(
  (response) => {
    const res = response.data;

    // 如果code不为200，表示请求异常
    if (res.code !== 200) {
      window.$message.error(res.msg || "上传失败");
      return Promise.reject(new Error(res.msg || "上传失败"));
    } else {
      return res;
    }
  },
  (error) => {
    console.error("上传响应错误：", error);

    // 处理HTTP状态码401
    if (error.response && error.response.status === 401) {
      localStorage.removeItem("token");
      window.location.href = "/#/login";
      return Promise.reject(error);
    }

    // 处理业务错误码401（如果response.data存在且code为401）
    if (
      error.response &&
      error.response.data &&
      error.response.data.code === 401
    ) {
      localStorage.removeItem("token");
      window.location.href = "/#/login";
      return Promise.reject(error);
    }

    window.$message.error(error.response.data.msg || "上传失败");
    return Promise.reject(error);
  }
);

export default service;
export { uploadService };
