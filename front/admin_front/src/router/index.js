import { createRouter, createWebHashHistory } from "vue-router";
import { getToken } from "@/utils/token";
import Layout from "@/layout/index.vue";

const routes = [
  {
    path: "/login",
    name: "Login",
    component: () => import("@/views/login/index.vue"),
    meta: { title: "登录", hidden: true },
  },
  {
    path: "/",
    component: Layout,
    redirect: "/dashboard",
    children: [
      {
        path: "dashboard",
        name: "Dashboard",
        component: () => import("@/views/dashboard/index.vue"),
        meta: { title: "控制台", icon: "Dashboard" },
      },
    ],
  },
  {
    path: "/user",
    component: Layout,
    redirect: "/user/list",
    meta: { title: "用户管理", icon: "Person" },
    children: [
      {
        path: "list",
        name: "UserList",
        component: () => import("@/views/user/list.vue"),
        meta: { title: "用户列表" },
      },
    ],
  },
  {
    path: "/course",
    component: Layout,
    redirect: "/course/list",
    meta: { title: "课程管理", icon: "Book" },
    children: [
      {
        path: "list",
        name: "CourseList",
        component: () => import("@/views/course/list.vue"),
        meta: { title: "课程列表" },
      },
      {
        path: "create",
        name: "CourseCreate",
        component: () => import("@/views/course/create.vue"),
        meta: { title: "创建课程", hidden: true },
      },
      {
        path: "edit/:id",
        name: "CourseEdit",
        component: () => import("@/views/course/edit.vue"),
        meta: { title: "编辑课程", hidden: true },
      },
      {
        path: "detail/:id",
        name: "CourseDetail",
        component: () => import("@/views/course/detail.vue"),
        meta: { title: "课程详情", hidden: true },
      },
    ],
  },
  {
    path: "/teacher",
    component: Layout,
    redirect: "/teacher/list",
    meta: { title: "讲师管理", icon: "School" },
    children: [
      {
        path: "list",
        name: "TeacherList",
        component: () => import("@/views/teacher/list.vue"),
        meta: { title: "讲师列表" },
      },
    ],
  },
  {
    path: "/live",
    component: Layout,
    redirect: "/live/list",
    meta: { title: "直播管理", icon: "Videocam" },
    children: [
      {
        path: "list",
        name: "LiveList",
        component: () => import("@/views/live/list.vue"),
        meta: { title: "直播列表" },
      },
      {
        path: "homepage",
        name: "LiveHomepage",
        component: () => import("@/views/live/homepage.vue"),
        meta: { title: "首页推荐" },
      },
      {
        path: "create",
        name: "LiveCreate",
        component: () => import("@/views/live/edit.vue"),
        meta: { title: "创建直播", hidden: true },
      },
      {
        path: "edit/:id",
        name: "LiveEdit",
        component: () => import("@/views/live/edit.vue"),
        meta: { title: "编辑直播", hidden: true },
      },
    ],
  },
  {
    path: "/review",
    component: Layout,
    redirect: "/review/list",
    meta: { title: "评价管理", icon: "ChatboxEllipses" },
    children: [
      {
        path: "list",
        name: "ReviewList",
        component: () => import("@/views/review/list.vue"),
        meta: { title: "评价列表" },
      },
    ],
  },
  {
    path: "/customer",
    component: Layout,
    redirect: "/customer/list",
    meta: { title: "客服管理", icon: "Call" },
    children: [
      {
        path: "list",
        name: "CustomerList",
        component: () => import("@/views/customer/list.vue"),
        meta: { title: "客服列表" },
      },
    ],
  },
  {
    path: "/checkin",
    component: Layout,
    redirect: "/checkin/list",
    meta: { title: "打卡管理", icon: "Calendar" },
    children: [
      {
        path: "list",
        name: "CheckinList",
        component: () => import("@/views/checkin/list.vue"),
        meta: { title: "打卡记录" },
      },
      {
        path: "statistics",
        name: "CheckinStatistics",
        component: () => import("@/views/checkin/statistics.vue"),
        meta: { title: "打卡统计" },
      },
    ],
  },
  {
    path: "/statistics",
    component: Layout,
    redirect: "/statistics/learn",
    meta: { title: "统计分析", icon: "BarChart" },
    children: [
      {
        path: "learn",
        name: "LearnStats",
        component: () => import("@/views/statistics/learn.vue"),
        meta: { title: "学习记录" },
      },
    ],
  },
  {
    path: "/permission",
    component: Layout,
    redirect: "/permission/course-access",
    meta: { title: "课程授权", icon: "Shield" },
    children: [
      {
        path: "course-access",
        name: "CourseAccessList",
        component: () => import("@/views/permission/course-access/list.vue"),
        meta: { title: "权限管理" },
      },
      {
        path: "course-access/statistics",
        name: "CourseAccessStatistics",
        component: () => import("@/views/permission/course-access/statistics.vue"),
        meta: { title: "权限统计" },
      },
    ],
  },
  {
    path: "/system",
    component: Layout,
    redirect: "/system/tag-config",
    meta: { title: "系统管理", icon: "Settings" },
    children: [
      {
        path: "tag-config",
        name: "TagConfig",
        component: () => import("@/views/system/tag-config.vue"),
        meta: { title: "标签配置" },
      },
    ],
  },
  {
    path: "/test",
    name: "Test",
    component: Layout,
    meta: { title: "测试页面", icon: "TestTube" },
    children: [
      {
        path: "oss-upload",
        name: "OssUploadTest",
        meta: { title: "OSS上传测试" },
        component: () => import("@/views/test/OssUploadTest.vue"),
      },
      {
        path: "cascader",
        name: "CascaderTest",
        meta: { title: "级联选择器测试" },
        component: () => import("@/views/test/CascaderTest.vue"),
      },
    ],
  },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title
    ? `${to.meta.title} - 鼎峰课堂管理系统`
    : "鼎峰课堂管理系统";

  // 使用工具函数获取token
  const token = getToken();

  // 如果访问登录页面且已登录，重定向到首页
  if (to.path === "/login" && token) {
    next({ path: "/" });
    return;
  }

  // 如果访问非登录页面且未登录，重定向到登录页
  if (to.path !== "/login" && !token) {
    next({ path: "/login" });
    return;
  }

  next();
});

export default router;
