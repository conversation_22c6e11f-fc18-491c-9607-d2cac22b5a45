<template>
  <n-select
    v-model:value="selectedValue"
    :options="options"
    :loading="loading"
    :placeholder="placeholder"
    :multiple="multiple"
    :clearable="clearable"
    :filterable="true"
    :remote="true"
    :clear-filter-after-select="false"
    :render-label="renderLabel"
    @search="handleSearch"
    @update:value="handleUpdate"
    @clear="handleClear"
  >
    <template #empty>
      <div style="text-align: center; padding: 12px;">
        {{ searchQuery ? '未找到匹配的用户' : '请输入用户名称搜索' }}
      </div>
    </template>
    
    <template #action>
      <div v-if="searchQuery && !loading && options.length === 0" style="padding: 8px; text-align: center; color: #999;">
        未找到用户 "{{ searchQuery }}"
      </div>
    </template>
  </n-select>
</template>

<script setup>
import { ref, watch, computed, h, onMounted } from 'vue'
import { getUserList } from '@/api/user'
import { useMessage } from 'naive-ui'

const props = defineProps({
  value: {
    type: [String, Number, Array],
    default: null
  },
  placeholder: {
    type: String,
    default: '请选择用户'
  },
  multiple: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: true
  },
  // 是否显示用户ID
  showUserId: {
    type: Boolean,
    default: true
  },
  // 防抖延迟时间（毫秒）
  debounceDelay: {
    type: Number,
    default: 300
  }
})

const emit = defineEmits(['update:value', 'change'])

const message = useMessage()
const loading = ref(false)
const options = ref([])
const searchQuery = ref('')
let debounceTimer = null

// 双向绑定
const selectedValue = computed({
  get: () => props.value,
  set: (value) => emit('update:value', value)
})

// 渲染标签 - 显示头像和用户名
const renderLabel = (option) => {
  const user = option.user
  if (!user) return option.label

  return h('div', {
    style: {
      display: 'flex',
      alignItems: 'center',
      gap: '8px'
    }
  }, [
    // 头像
    h('img', {
      src: user.avatarFullUrl || user.avatarUrl || '/default-avatar.svg',
      style: {
        width: '24px',
        height: '24px',
        borderRadius: '50%',
        objectFit: 'cover',
        flexShrink: 0
      },
      onError: (e) => {
        e.target.src = '/default-avatar.svg'
      }
    }),
    // 用户名和ID
    h('span', {
      style: {
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap'
      }
    }, option.label)
  ])
}

onMounted(() => {
  handleSearch('')
})

// 实际搜索用户的函数
const performSearch = async (query) => {
  loading.value = true
  try {
    const { data } = await getUserList({
      page: 1,
      pageSize: 10,
      searchKeyWord: query, // 按用户名搜索
      status: 1 // 只搜索有效用户
    })
    
    options.value = (data.records || []).map(user => ({
      label: props.showUserId 
        ? `${user.nickname||user.username} (ID: ${user.id})`
        : user.nickname||user.username,
      value: user.id,
      user: user // 保存完整用户信息
    }))
  } catch (error) {
    message.error('搜索用户失败：' + error.message)
    options.value = []
  } finally {
    loading.value = false
  }
}

// 带防抖的搜索用户函数
const handleSearch = (query) => {
  searchQuery.value = query
  
  // 清除之前的定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }
  
  // 如果是空查询，立即执行搜索
  if (!query) {
    performSearch(query)
    return
  }
  
  // 设置新的防抖定时器
  debounceTimer = setTimeout(() => {
    performSearch(query)
  }, props.debounceDelay)
}

// 值更新处理
const handleUpdate = (value) => {
  emit('update:value', value)
  emit('change', value, getSelectedUsers(value))
}

// 清空处理
const handleClear = () => {
  searchQuery.value = ''
  options.value = []
  emit('update:value', props.multiple ? [] : null)
  emit('change', props.multiple ? [] : null, [])
}

// 获取选中的用户信息
const getSelectedUsers = (value) => {
  if (!value) return []
  
  const values = Array.isArray(value) ? value : [value]
  return options.value
    .filter(option => values.includes(option.value))
    .map(option => option.user)
}

// 初始化时如果有值，需要加载对应的用户信息
const loadInitialUsers = async () => {
  if (!props.value) return
  
  const values = Array.isArray(props.value) ? props.value : [props.value]
  if (values.length === 0) return
  
  loading.value = true
  try {
    // 这里可以调用批量获取用户信息的API
    // 暂时使用单个查询的方式
    const userPromises = values.map(async (userId) => {
      try {
        const { data } = await getUserList({
          page: 1,
          pageSize: 1,
          userId: userId
        })
        return data.records?.[0]
      } catch (error) {
        console.warn(`获取用户 ${userId} 信息失败:`, error)
        return null
      }
    })
    
    const users = await Promise.all(userPromises)
    options.value = users
      .filter(user => user)
      .map(user => ({
        label: props.showUserId 
          ? `${user.nickname||user.username} (ID: ${user.id})`
          : user.nickname||user.username,
        value: user.id,
        user: user
      }))
  } catch (error) {
    console.error('加载初始用户信息失败:', error)
  } finally {
    loading.value = false
  }
}

// 监听value变化，加载初始数据
watch(
  () => props.value,
  (newValue) => {
    if (newValue && options.value.length === 0) {
      loadInitialUsers()
    }
  },
  { immediate: true }
)

// 暴露方法给父组件
defineExpose({
  search: handleSearch,
  clear: handleClear,
  getSelectedUsers
})
</script>

<style lang="scss" scoped>
// 可以添加自定义样式
</style>
