<template>
  <div class="access-statistics">
    <!-- 页面标题 -->
    <n-page-header title="权限统计" subtitle="查看系统权限数据统计">
      <template #extra>
        <n-space style="margin: 10px 0;">
          <n-button @click="$router.push('/permission/course-access')">
            <template #icon>
              <n-icon><ArrowBackOutline /></n-icon>
            </template>
            返回权限列表
          </n-button>
          <n-button type="primary" @click="refreshData">
            <template #icon>
              <n-icon><RefreshOutline /></n-icon>
            </template>
            刷新数据
          </n-button>
        </n-space>
      </template>
    </n-page-header>

    <!-- 统计卡片 -->
    <n-grid :cols="4" :x-gap="16" :y-gap="16" class="stats-grid">
      <n-grid-item>
        <n-card>
          <n-statistic label="总权限数" :value="globalStats.totalAccess">
            <template #prefix>
              <n-icon size="18" color="#18a058">
                <ShieldCheckmarkOutline />
              </n-icon>
            </template>
          </n-statistic>
        </n-card>
      </n-grid-item>
      
      <n-grid-item>
        <n-card>
          <n-statistic label="有效权限" :value="globalStats.validAccess">
            <template #prefix>
              <n-icon size="18" color="#2080f0">
                <CheckmarkCircleOutline />
              </n-icon>
            </template>
          </n-statistic>
        </n-card>
      </n-grid-item>
      
      <n-grid-item>
        <n-card>
          <n-statistic label="买断权限" :value="globalStats.buyoutAccess">
            <template #prefix>
              <n-icon size="18" color="#f0a020">
                <StarOutline />
              </n-icon>
            </template>
          </n-statistic>
        </n-card>
      </n-grid-item>
      
      <n-grid-item>
        <n-card>
          <n-statistic label="即将过期" :value="globalStats.expiringAccess">
            <template #prefix>
              <n-icon size="18" color="#d03050">
                <TimeOutline />
              </n-icon>
            </template>
          </n-statistic>
        </n-card>
      </n-grid-item>
    </n-grid>

    <!-- 详细统计 -->
    <n-grid :cols="2" :x-gap="16" :y-gap="16" class="detail-stats">
      <!-- 权限类型分布 -->
      <n-grid-item>
        <n-card title="权限类型分布">
          <div class="chart-container">
            <div class="stat-item" v-for="item in accessTypeStats" :key="item.type">
              <div class="stat-label">{{ item.label }}</div>
              <div class="stat-value">{{ item.count }}</div>
              <div class="stat-bar">
                <div 
                  class="stat-bar-fill" 
                  :style="{ width: item.percentage + '%', backgroundColor: item.color }"
                ></div>
              </div>
              <div class="stat-percentage">{{ item.percentage }}%</div>
            </div>
          </div>
        </n-card>
      </n-grid-item>

      <!-- 获取方式分布 -->
      <n-grid-item>
        <n-card title="获取方式分布">
          <div class="chart-container">
            <div class="stat-item" v-for="item in acquireMethodStats" :key="item.method">
              <div class="stat-label">{{ item.label }}</div>
              <div class="stat-value">{{ item.count }}</div>
              <div class="stat-bar">
                <div 
                  class="stat-bar-fill" 
                  :style="{ width: item.percentage + '%', backgroundColor: item.color }"
                ></div>
              </div>
              <div class="stat-percentage">{{ item.percentage }}%</div>
            </div>
          </div>
        </n-card>
      </n-grid-item>
    </n-grid>

    <!-- 用户权限查询 -->
    <n-card title="用户权限查询" class="user-query-card">
      <n-form
        ref="queryFormRef"
        :model="queryForm"
        label-placement="left"
        label-width="100px"
        class="query-form"
      >
        <n-grid :cols="24" :x-gap="16">
          <n-form-item-gi :span="12" label="选择用户" path="userId">
            <UserSelector
              v-model:value="queryForm.userId"
              placeholder="请搜索并选择用户"
              clearable
              @change="handleUserChange"
            />
          </n-form-item-gi>
          <n-form-item-gi :span="6">
            <n-button type="primary" @click="queryUserStats" :loading="queryLoading">
              <template #icon>
                <n-icon><SearchOutline /></n-icon>
              </template>
              查询
            </n-button>
          </n-form-item-gi>
        </n-grid>
      </n-form>

      <!-- 用户权限统计结果 -->
      <div v-if="userStats" class="user-stats-result">
        <n-divider title-placement="left">用户权限统计</n-divider>
        <n-grid :cols="4" :x-gap="16" :y-gap="16">
          <n-grid-item>
            <n-statistic label="总权限数" :value="userStats.totalAccess" />
          </n-grid-item>
          <n-grid-item>
            <n-statistic label="有效权限" :value="userStats.validAccess" />
          </n-grid-item>
          <n-grid-item>
            <n-statistic label="买断权限" :value="userStats.buyoutAccess" />
          </n-grid-item>
          <n-grid-item>
            <n-statistic label="即将过期" :value="userStats.expiringAccess" />
          </n-grid-item>
        </n-grid>
        
        <n-grid :cols="3" :x-gap="16" :y-gap="16" style="margin-top: 16px">
          <n-grid-item>
            <n-statistic label="总课程数" :value="userStats.totalCourses" />
          </n-grid-item>
          <n-grid-item>
            <n-statistic label="总章节数" :value="userStats.totalChapters" />
          </n-grid-item>
          <n-grid-item>
            <n-statistic label="总课时数" :value="userStats.totalLessons" />
          </n-grid-item>
        </n-grid>
      </div>
    </n-card>

    <!-- 最近权限变动 -->
    <n-card title="最近权限变动">
      <n-data-table
        :columns="recentColumns"
        :data="recentAccessData"
        :loading="recentLoading"
        :pagination="false"
        size="small"
        max-height="400px"
      />
    </n-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useMessage } from 'naive-ui'
import {
  ArrowBackOutline,
  RefreshOutline,
  ShieldCheckmarkOutline,
  CheckmarkCircleOutline,
  StarOutline,
  TimeOutline,
  SearchOutline
} from '@vicons/ionicons5'
import {
  getGlobalAccessStatistics,
  getUserAccessStatistics,
  getUserAccessList,
  formatAccessType,
  formatAcquireMethod,
  formatDateTime
} from '@/api/permission'
import UserSelector from '@/components/Selector/UserSelector.vue'

const message = useMessage()

// 响应式数据
const globalStats = ref({
  totalAccess: 0,
  validAccess: 0,
  buyoutAccess: 0,
  expiringAccess: 0,
  expiredAccess: 0,
  freeAccess: 0,
  purchasedAccess: 0
})

const userStats = ref(null)
const recentAccessData = ref([])
const queryLoading = ref(false)
const recentLoading = ref(false)

// 查询表单
const queryForm = reactive({
  userId: null
})

// 选中的用户信息
const selectedUser = ref(null)

// 权限类型统计
const accessTypeStats = computed(() => {
  const total = globalStats.value.totalAccess || 1
  const colors = ['#18a058', '#2080f0', '#f0a020']
  
  return [
    {
      type: 1,
      label: '课程权限',
      count: Math.floor(total * 0.6), // 模拟数据
      percentage: Math.round((Math.floor(total * 0.6) / total) * 100),
      color: colors[0]
    },
    {
      type: 2,
      label: '章节权限',
      count: Math.floor(total * 0.3), // 模拟数据
      percentage: Math.round((Math.floor(total * 0.3) / total) * 100),
      color: colors[1]
    },
    {
      type: 3,
      label: '课时权限',
      count: Math.floor(total * 0.1), // 模拟数据
      percentage: Math.round((Math.floor(total * 0.1) / total) * 100),
      color: colors[2]
    }
  ]
})

// 获取方式统计
const acquireMethodStats = computed(() => {
  const total = globalStats.value.totalAccess || 1
  const colors = ['#d03050', '#18a058', '#2080f0', '#f0a020', '#722ed1', '#eb2f96']
  
  return [
    {
      method: 1,
      label: '购买',
      count: globalStats.value.purchasedAccess || 0,
      percentage: Math.round(((globalStats.value.purchasedAccess || 0) / total) * 100),
      color: colors[0]
    },
    {
      method: 2,
      label: '免费',
      count: globalStats.value.freeAccess || 0,
      percentage: Math.round(((globalStats.value.freeAccess || 0) / total) * 100),
      color: colors[1]
    },
    {
      method: 3,
      label: '积分兑换',
      count: Math.floor(total * 0.1), // 模拟数据
      percentage: 10,
      color: colors[2]
    },
    {
      method: 4,
      label: '优惠券',
      count: Math.floor(total * 0.05), // 模拟数据
      percentage: 5,
      color: colors[3]
    },
    {
      method: 5,
      label: '管理员赠送',
      count: Math.floor(total * 0.03), // 模拟数据
      percentage: 3,
      color: colors[4]
    },
    {
      method: 6,
      label: '推广活动',
      count: Math.floor(total * 0.02), // 模拟数据
      percentage: 2,
      color: colors[5]
    }
  ]
})

// 最近权限变动表格列
const recentColumns = [
  {
    title: '用户ID',
    key: 'userId',
    width: 100
  },
  {
    title: '课程ID',
    key: 'courseId',
    width: 100
  },
  {
    title: '权限类型',
    key: 'accessType',
    width: 120,
    render(row) {
      return formatAccessType(row.accessType)
    }
  },
  {
    title: '获取方式',
    key: 'acquireMethod',
    width: 120,
    render(row) {
      return formatAcquireMethod(row.acquireMethod)
    }
  },
  {
    title: '创建时间',
    key: 'createdAt',
    width: 180,
    render(row) {
      return formatDateTime(row.createdAt)
    }
  }
]

// 获取全局统计数据
const loadGlobalStats = async () => {
  try {
    const { data } = await getGlobalAccessStatistics()
    globalStats.value = data
  } catch (error) {
    message.error('获取全局统计失败：' + error.message)
  }
}

// 获取最近权限变动
const loadRecentAccess = async () => {
  recentLoading.value = true
  try {
    const { data } = await getUserAccessList({
      page: 1,
      pageSize: 10,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    })
    recentAccessData.value = data.records || []
  } catch (error) {
    message.error('获取最近权限变动失败：' + error.message)
  } finally {
    recentLoading.value = false
  }
}

// 用户选择变化处理
const handleUserChange = (value, users) => {
  selectedUser.value = users?.[0] || null
}

// 查询用户权限统计
const queryUserStats = async () => {
  if (!queryForm.userId) {
    message.warning('请选择用户')
    return
  }

  queryLoading.value = true
  try {
    const { data } = await getUserAccessStatistics(queryForm.userId)
    userStats.value = data
    message.success('查询成功')
  } catch (error) {
    message.error('查询用户权限统计失败：' + error.message)
    userStats.value = null
  } finally {
    queryLoading.value = false
  }
}

// 刷新数据
const refreshData = async () => {
  await Promise.all([
    loadGlobalStats(),
    loadRecentAccess()
  ])
  message.success('数据刷新成功')
}

// 初始化
onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.access-statistics {
  padding: 24px;

  .stats-grid {
    margin-bottom: 24px;
  }

  .detail-stats {
    margin-bottom: 24px;
  }

  .user-query-card {
    margin-bottom: 24px;
  }
  
  .chart-container {
    .stat-item {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      
      .stat-label {
        width: 80px;
        font-size: 14px;
        color: #666;
      }
      
      .stat-value {
        width: 60px;
        font-weight: bold;
        text-align: right;
        margin-right: 12px;
      }
      
      .stat-bar {
        flex: 1;
        height: 8px;
        background-color: #f0f0f0;
        border-radius: 4px;
        overflow: hidden;
        margin-right: 12px;
        
        .stat-bar-fill {
          height: 100%;
          transition: width 0.3s ease;
        }
      }
      
      .stat-percentage {
        width: 40px;
        font-size: 12px;
        color: #999;
        text-align: right;
      }
    }
  }
  
  .user-stats-result {
    margin-top: 24px;
  }
  
  .query-form {
    .n-form-item {
      margin-bottom: 0;
    }
  }
}
</style>
