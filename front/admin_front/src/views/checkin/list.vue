<template>
  <div class="page-container">
    <n-card title="打卡记录管理" class="content-card">
      <!-- 搜索栏 -->
      <div class="search-bar">
        <n-form inline :label-width="80">
          <n-form-item label="用户">
            <UserSelector
              v-model:value="searchParams.userId"
              placeholder="请选择用户"
              style="width: 200px"
              :show-user-id="false"
              @change="handleUserChange"
            />
          </n-form-item>
          <n-form-item label="打卡日期">
            <n-date-picker
              v-model:value="searchParams.dateRange"
              type="daterange"
              clearable
              placeholder="选择日期范围"
              style="width: 240px"
            />
          </n-form-item>
          <n-form-item label="学习时长">
            <n-input-number
              v-model:value="searchParams.minDuration"
              placeholder="最少分钟"
              style="width: 120px"
              :min="0"
            />
            <span style="margin: 0 8px">-</span>
            <n-input-number
              v-model:value="searchParams.maxDuration"
              placeholder="最多分钟"
              style="width: 120px"
              :min="0"
            />
          </n-form-item>
          <n-form-item>
            <n-button type="primary" @click="handleSearch">
              <template #icon>
                <n-icon><SearchOutline /></n-icon>
              </template>
              搜索
            </n-button>
            <n-button class="m-l-10" @click="resetSearch">
              <template #icon>
                <n-icon><RefreshOutline /></n-icon>
              </template>
              重置
            </n-button>
          </n-form-item>
        </n-form>
      </div>

      <!-- 操作按钮 -->
      <div class="action-bar">
        <n-button
          type="error"
          :disabled="!selectedRowKeys.length"
          @click="handleBatchDelete"
        >
          <template #icon>
            <n-icon><TrashOutline /></n-icon>
          </template>
          批量删除
        </n-button>
        <n-button class="m-l-10" @click="handleExport">
          <template #icon>
            <n-icon><DownloadOutline /></n-icon>
          </template>
          导出数据
        </n-button>
      </div>

      <!-- 数据表格 -->
      <n-data-table
        ref="table"
        remote
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row) => row.id"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
        @update:checked-row-keys="handleCheckedRowKeysChange"
      />
    </n-card>

    <!-- 打卡详情弹窗 -->
    <n-modal
      v-model:show="showDetailModal"
      preset="card"
      title="打卡详情"
      style="width: 600px"
    >
      <CheckinDetailCard
        v-if="currentCheckin"
        :checkin="currentCheckin"
        @close="showDetailModal = false"
      />
    </n-modal>
  </div>
</template>

<script setup>
import { h, ref, reactive, computed, onMounted } from "vue";
import {
  SearchOutline,
  RefreshOutline,
  TrashOutline,
  DownloadOutline,
  EyeOutline,
  CalendarOutline,
  TimeOutline,
  PersonOutline,
} from "@vicons/ionicons5";
import {
  getUserCheckins,
  deleteCheckin,
  batchDeleteCheckins,
} from "@/api/userInteraction";
import { useMessage, useDialog } from "naive-ui";
import dayjs from "dayjs";
import CheckinDetailCard from "./CheckinDetailCard.vue";
import UserSelector from "@/components/Selector/UserSelector.vue";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";

const message = useMessage();
const dialog = useDialog();

// 表格数据
const loading = ref(false);
const tableData = ref([]);
const selectedRowKeys = ref([]);

// 搜索参数
const searchParams = reactive({
  userId: null,
  username: "",
  dateRange: null,
  minDuration: null,
  maxDuration: null,
});

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  pageCount: 1,
  showSizePicker: true,
  pageSizes: [10, 20, 30, 50],
  itemCount: 0,
});

// 弹窗状态
const showDetailModal = ref(false);
const currentCheckin = ref(null);

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return "-";
  return dayjs(dateTimeStr).format("YYYY-MM-DD HH:mm:ss");
};

// 格式化学习时长
const formatDuration = (minutes) => {
  if (!minutes) return "-";
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  if (hours > 0) {
    return `${hours}小时${mins}分钟`;
  }
  return `${mins}分钟`;
};

// 表格列配置
const createColumns = () => {
  return [
    {
      type: "selection",
      align: "center",
    },
    {
      title: "ID",
      key: "id",
      width: 80,
      align: "center",
    },
    {
      title: "用户名",
      key: "username",
      width: 140,
      align: "center",
      render(row) {
        return h(
          "n-tag",
          {
            type: "info",
            size: "small",
            style: "background-color: #f0f9ff; color: #0369a1; border: 1px solid #bae6fd;",
          },
          {
            default: () => row.username || "-",
            icon: () =>
              h(
                "n-icon",
                { size: 14, style: "margin-right: 4px;" },
                { default: () => h(PersonOutline) }
              ),
          }
        );
      },
    },
    {
      title: "打卡日期",
      key: "checkinDate",
      width: 120,
      align: "center",
      render(row) {
        return h(
          "n-tag",
          {
            type: "success",
            size: "small",
            style: "background-color: #f0fdf4; color: #166534; border: 1px solid #bbf7d0;",
          },
          {
            default: () => dayjs(row.checkinDate).format("MM-DD"),
            icon: () =>
              h(
                "n-icon",
                { size: 14, style: "margin-right: 4px;" },
                { default: () => h(CalendarOutline) }
              ),
          }
        );
      },
    },
    {
      title: "打卡时间",
      key: "checkinTime",
      width: 180,
      align: "center",
      render(row) {
        return formatDateTime(row.checkinTime);
      },
    },
    {
      title: "学习时长",
      key: "learnDuration",
      width: 120,
      align: "center",
      render(row) {
        const duration = row.learnDuration || 0;
        const color = duration >= 120 ? "#16a34a" : duration >= 60 ? "#f59e0b" : "#dc2626";
        return h(
          "n-tag",
          {
            type: "warning",
            size: "small",
            style: `background-color: ${color}20; color: ${color}; border: 1px solid ${color}40;`,
          },
          {
            default: () => formatDuration(duration),
            icon: () =>
              h(
                "n-icon",
                { size: 14, style: "margin-right: 4px;" },
                { default: () => h(TimeOutline) }
              ),
          }
        );
      },
    },
    {
      title: "打卡备注",
      key: "remark",
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        return row.remark || "-";
      },
    },
    {
      title: "操作",
      key: "actions",
      width: 150,
      fixed: "right",
      align: "center",
      render(row) {
        return h(
          "div",
          { style: "display: flex; justify-content: center; gap: 8px;" },
          [
            h(
              "n-button",
              {
                size: "small",
                type: "primary",
                style: "padding: 0 12px; border-radius: 15px; background-color: #1890ff; border: none; color: white;",
                onClick: () => handleView(row),
              },
              {
                default: () => "查看",
                icon: () =>
                  h(
                    "n-icon",
                    { size: 14, style: "color: white;" },
                    { default: () => h(EyeOutline) }
                  ),
              }
            ),
            h(
              "n-button",
              {
                size: "small",
                type: "error",
                style: "padding: 0 12px; border-radius: 15px; background-color: #f5222d; border: none; color: white;",
                onClick: () => handleDelete(row),
              },
              {
                default: () => "删除",
                icon: () =>
                  h(
                    "n-icon",
                    { size: 14, style: "color: white;" },
                    { default: () => h(TrashOutline) }
                  ),
              }
            ),
          ]
        );
      },
    },
  ];
};

const columns = createColumns();

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    const params = {
      pageNum: pagination.page,
      pageSize: pagination.pageSize,
      userId: searchParams.userId || undefined,
      username: searchParams.username || undefined,
      startDate: searchParams.dateRange?.[0] ? dayjs(searchParams.dateRange[0]).format("YYYY-MM-DD") : undefined,
      endDate: searchParams.dateRange?.[1] ? dayjs(searchParams.dateRange[1]).format("YYYY-MM-DD") : undefined,
      minDuration: searchParams.minDuration || undefined,
      maxDuration: searchParams.maxDuration || undefined,
    };

    const response = await getUserCheckins(params);

    if (response.code === 200) {
      tableData.value = response.data.records || [];
      pagination.itemCount = response.data.total || 0;
      pagination.pageCount = Math.ceil(pagination.itemCount / pagination.pageSize);
    } else {
      message.error(response.message || "加载数据失败");
    }
  } catch (error) {
    console.error("加载数据失败:", error);
    message.error("加载数据失败");
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.page = 1;
  loadData();
};

// 重置搜索
const resetSearch = () => {
  Object.assign(searchParams, {
    userId: null,
    username: "",
    dateRange: null,
    minDuration: null,
    maxDuration: null,
  });
  pagination.page = 1;
  loadData();
};

// 用户选择变化处理
const handleUserChange = (userId, users) => {
  if (users && users.length > 0) {
    searchParams.username = users[0].username;
  } else {
    searchParams.username = "";
  }
};

// 分页处理
const handlePageChange = (page) => {
  pagination.page = page;
  loadData();
};

const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize;
  pagination.page = 1;
  loadData();
};

// 选择行处理
const handleCheckedRowKeysChange = (keys) => {
  selectedRowKeys.value = keys;
};

// 查看详情
const handleView = (row) => {
  currentCheckin.value = row;
  showDetailModal.value = true;
};

// 删除单条记录
const handleDelete = (row) => {
  dialog.warning({
    title: "确认删除",
    content: `确定要删除用户 ${row.username} 在 ${dayjs(row.checkinDate).format("YYYY-MM-DD")} 的打卡记录吗？`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        const response = await deleteCheckin(row.id);
        if (response.code === 200) {
          message.success("删除成功");
          loadData();
        } else {
          message.error(response.message || "删除失败");
        }
      } catch (error) {
        console.error("删除失败:", error);
        message.error("删除失败");
      }
    },
  });
};

// 批量删除
const handleBatchDelete = () => {
  if (!selectedRowKeys.value.length) {
    message.warning("请选择要删除的记录");
    return;
  }

  dialog.warning({
    title: "确认批量删除",
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 条打卡记录吗？`,
    positiveText: "确定",
    negativeText: "取消",
    onPositiveClick: async () => {
      try {
        const response = await batchDeleteCheckins(selectedRowKeys.value);
        if (response.code === 200) {
          message.success("批量删除成功");
          selectedRowKeys.value = [];
          loadData();
        } else {
          message.error(response.message || "批量删除失败");
        }
      } catch (error) {
        console.error("批量删除失败:", error);
        message.error("批量删除失败");
      }
    },
  });
};

// 导出数据
const handleExport = async () => {
  try {
    message.loading("正在导出数据...", { duration: 0 });

    // 获取所有数据（不分页）
    const params = {
      pageNum: 1,
      pageSize: 10000, // 获取大量数据
      userId: searchParams.userId || undefined,
      username: searchParams.username || undefined,
      startDate: searchParams.dateRange?.[0] ? dayjs(searchParams.dateRange[0]).format("YYYY-MM-DD") : undefined,
      endDate: searchParams.dateRange?.[1] ? dayjs(searchParams.dateRange[1]).format("YYYY-MM-DD") : undefined,
      minDuration: searchParams.minDuration || undefined,
      maxDuration: searchParams.maxDuration || undefined,
    };

    const response = await getUserCheckins(params);

    if (response.code === 200) {
      const data = response.data.records || [];

      // 准备导出数据
      const exportData = data.map((item, index) => ({
        '序号': index + 1,
        '用户名': item.username || '-',
        '打卡日期': dayjs(item.checkinDate).format("YYYY-MM-DD"),
        '打卡时间': formatDateTime(item.checkinTime),
        '学习时长(分钟)': item.learnDuration || 0,
        '学习时长(小时)': item.learnDuration ? (item.learnDuration / 60).toFixed(1) : '0.0',
        '打卡备注': item.remark || '-',
        '创建时间': formatDateTime(item.createdAt)
      }));

      // 创建工作簿
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(exportData);

      // 设置列宽
      const colWidths = [
        { wch: 8 },  // 序号
        { wch: 15 }, // 用户名
        { wch: 12 }, // 打卡日期
        { wch: 20 }, // 打卡时间
        { wch: 15 }, // 学习时长(分钟)
        { wch: 15 }, // 学习时长(小时)
        { wch: 30 }, // 打卡备注
        { wch: 20 }  // 创建时间
      ];
      ws['!cols'] = colWidths;

      // 添加工作表
      XLSX.utils.book_append_sheet(wb, ws, "打卡记录");

      // 生成文件名
      const now = dayjs().format("YYYY-MM-DD_HH-mm-ss");
      const filename = `打卡记录_${now}.xlsx`;

      // 导出文件
      const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
      const blob = new Blob([wbout], { type: 'application/octet-stream' });
      saveAs(blob, filename);

      message.destroyAll();
      message.success(`导出成功！共导出 ${exportData.length} 条记录`);
    } else {
      message.destroyAll();
      message.error(response.message || "导出失败");
    }
  } catch (error) {
    message.destroyAll();
    console.error("导出失败:", error);
    message.error("导出失败");
  }
};

onMounted(() => {
  loadData();
});
</script>

<style lang="scss" scoped>
.page-container {
  padding: 20px;
}

.content-card {
  margin-bottom: 20px;
}

.search-bar {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 8px;
}

.action-bar {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.m-l-10 {
  margin-left: 10px;
}

:deep(.n-data-table-th) {
  background-color: #f8f9fa;
  font-weight: 600;
}

:deep(.n-data-table-td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.n-data-table-tr:hover .n-data-table-td) {
  background-color: #f8f9fa;
}
</style>
