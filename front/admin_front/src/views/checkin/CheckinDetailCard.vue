<template>
  <div class="checkin-detail">
    <n-descriptions
      :column="2"
      label-placement="left"
      label-style="font-weight: 600; color: #666;"
      content-style="color: #333;"
    >
      <n-descriptions-item label="打卡ID">
        <n-tag type="info" size="small">{{ checkin.id }}</n-tag>
      </n-descriptions-item>
      
      <n-descriptions-item label="用户名">
        <div class="user-info">
          <n-avatar
            :src="checkin.userAvatar || defaultAvatar"
            size="small"
            round
            style="margin-right: 8px"
          />
          <span>{{ checkin.username || "-" }}</span>
        </div>
      </n-descriptions-item>

      <n-descriptions-item label="打卡日期">
        <n-tag type="success" size="small">
          <template #icon>
            <n-icon><CalendarOutline /></n-icon>
          </template>
          {{ formatDate(checkin.checkinDate) }}
        </n-tag>
      </n-descriptions-item>

      <n-descriptions-item label="打卡时间">
        <n-tag type="warning" size="small">
          <template #icon>
            <n-icon><TimeOutline /></n-icon>
          </template>
          {{ formatTime(checkin.checkinTime) }}
        </n-tag>
      </n-descriptions-item>

      <n-descriptions-item label="学习时长">
        <div class="duration-info">
          <n-progress
            type="circle"
            :percentage="getDurationPercentage(checkin.learnDuration)"
            :stroke-width="8"
            :show-indicator="false"
            style="width: 60px; height: 60px; margin-right: 12px"
          />
          <div class="duration-text">
            <div class="duration-value">{{ formatDuration(checkin.learnDuration) }}</div>
            <div class="duration-label">学习时长</div>
          </div>
        </div>
      </n-descriptions-item>

      <n-descriptions-item label="连续打卡">
        <n-statistic
          :value="checkin.consecutiveDays || 0"
          suffix="天"
          style="color: #18a058"
        >
          <template #prefix>
            <n-icon color="#18a058"><FlameOutline /></n-icon>
          </template>
        </n-statistic>
      </n-descriptions-item>

      <n-descriptions-item label="创建时间" :span="2">
        {{ formatDateTime(checkin.createdAt) }}
      </n-descriptions-item>

      <n-descriptions-item label="打卡备注" :span="2">
        <div class="remark-content">
          <n-input
            v-if="checkin.remark"
            :value="checkin.remark"
            type="textarea"
            readonly
            :autosize="{ minRows: 2, maxRows: 4 }"
            style="background-color: #f8f9fa;"
          />
          <n-empty
            v-else
            description="暂无备注"
            size="small"
            style="margin: 20px 0;"
          />
        </div>
      </n-descriptions-item>
    </n-descriptions>

    <!-- 打卡统计信息 -->
    <n-divider title-placement="left">
      <n-icon><BarChartOutline /></n-icon>
      打卡统计
    </n-divider>

    <n-grid :cols="3" :x-gap="16">
      <n-gi>
        <n-card size="small" class="stat-card">
          <n-statistic label="本月打卡" :value="monthlyStats.checkinCount" suffix="次">
            <template #prefix>
              <n-icon color="#1890ff"><CalendarOutline /></n-icon>
            </template>
          </n-statistic>
        </n-card>
      </n-gi>
      <n-gi>
        <n-card size="small" class="stat-card">
          <n-statistic label="累计时长" :value="monthlyStats.totalDuration" suffix="小时">
            <template #prefix>
              <n-icon color="#52c41a"><TimeOutline /></n-icon>
            </template>
          </n-statistic>
        </n-card>
      </n-gi>
      <n-gi>
        <n-card size="small" class="stat-card">
          <n-statistic label="平均时长" :value="monthlyStats.avgDuration" suffix="分钟">
            <template #prefix>
              <n-icon color="#fa8c16"><TrendingUpOutline /></n-icon>
            </template>
          </n-statistic>
        </n-card>
      </n-gi>
    </n-grid>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <n-button @click="$emit('close')">关闭</n-button>
      <!-- <n-button type="primary" @click="handleViewUserProfile">
        <template #icon>
          <n-icon><PersonOutline /></n-icon>
        </template>
        查看用户资料
      </n-button> -->
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";
import {
  CalendarOutline,
  TimeOutline,
  FlameOutline,
  BarChartOutline,
  PersonOutline,
  TrendingUpOutline,
} from "@vicons/ionicons5";
import dayjs from "dayjs";
import { useMessage } from "naive-ui";

const props = defineProps({
  checkin: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(["close"]);

const message = useMessage();

// 默认头像
const defaultAvatar = "https://st4.depositphotos.com/8440746/30246/v/450/depositphotos_302460072-stock-illustration-study-icon-vector-male-student.jpg";

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return "-";
  return dayjs(dateStr).format("YYYY年MM月DD日");
};

// 格式化时间
const formatTime = (dateTimeStr) => {
  if (!dateTimeStr) return "-";
  return dayjs(dateTimeStr).format("HH:mm:ss");
};

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return "-";
  return dayjs(dateTimeStr).format("YYYY-MM-DD HH:mm:ss");
};

// 格式化学习时长
const formatDuration = (minutes) => {
  if (!minutes) return "0分钟";
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  if (hours > 0) {
    return `${hours}小时${mins}分钟`;
  }
  return `${mins}分钟`;
};

// 获取学习时长百分比（基于2小时为100%）
const getDurationPercentage = (minutes) => {
  if (!minutes) return 0;
  const maxMinutes = 120; // 2小时
  return Math.min((minutes / maxMinutes) * 100, 100);
};

// 模拟月度统计数据
const monthlyStats = computed(() => {
  return {
    checkinCount: 18,
    totalDuration: 36.5,
    avgDuration: 121,
  };
});

// 查看用户资料
const handleViewUserProfile = () => {
  message.info("跳转到用户详情页面...");
  // 这里可以实现跳转到用户详情页面的逻辑
};
</script>

<style lang="scss" scoped>
.checkin-detail {
  padding: 16px 0;
}

.user-info {
  display: flex;
  align-items: center;
}

.duration-info {
  display: flex;
  align-items: center;
}

.duration-text {
  .duration-value {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }
  
  .duration-label {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
  }
}

.remark-content {
  margin-top: 8px;
}

.stat-card {
  text-align: center;
  
  :deep(.n-statistic) {
    .n-statistic-label {
      font-size: 12px;
      color: #666;
    }
    
    .n-statistic-value {
      font-size: 18px;
      font-weight: 600;
    }
  }
}

.action-buttons {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
